# Super Mario 2D Game

A classic 2D Mario-style platformer game built with HTML5 Canvas and JavaScript.

## Features

- **Classic Mario gameplay** with jumping, running, and platforming
- **Enemy interactions** - Jump on enemies to defeat them
- **Collectible coins** for scoring
- **Multiple platforms** and level design
- **Lives system** with invulnerability frames
- **Sound effects** using Web Audio API
- **Responsive design** that works on different screen sizes
- **Game states** - Start, pause, game over, level complete

## How to Play

### Controls
- **Arrow Keys (Left/Right)**: Move Mario left and right
- **Spacebar**: Jump
- **R**: Restart the game
- **P**: Pause/Resume the game

### Gameplay
1. Use the arrow keys to move <PERSON> around the level
2. Press spacebar to jump on platforms and over obstacles
3. Collect golden coins to increase your score (50 points each)
4. Jump on brown enemies (Goombas) to defeat them (100 points each)
5. Avoid touching enemies from the side or you'll lose a life
6. Collect all coins to complete the level
7. You start with 3 lives - game over when you lose them all

### Scoring
- **Coins**: 50 points each
- **Defeating enemies**: 100 points each

## Running the Game

### Option 1: Local Web Server (Recommended)
1. Open a terminal in the game directory
2. Run a local web server:
   ```bash
   # Python 3
   python -m http.server 3000
   
   # Python 2
   python -m SimpleHTTPServer 3000
   
   # Node.js (if you have http-server installed)
   npx http-server -p 3000
   ```
3. Open your browser and go to `http://localhost:3000`

### Option 2: Direct File Opening
1. Simply open `index.html` in your web browser
2. Note: Some features may not work due to browser security restrictions

## Game Architecture

### Files Structure
- `index.html` - Main HTML file with game UI
- `style.css` - Styling and responsive design
- `game.js` - Main game logic, physics, and rendering
- `sounds.js` - Sound effects using Web Audio API

### Key Classes
- **Mario**: Player character with physics, movement, and collision detection
- **Platform**: Static platforms for jumping and level design
- **Enemy**: Moving enemies with AI and collision detection
- **Coin**: Collectible items with rotation animation
- **SoundManager**: Handles all game sound effects

### Game Loop
The game uses `requestAnimationFrame` for smooth 60fps gameplay:
1. Clear canvas
2. Update all game objects (physics, AI, input)
3. Check collisions
4. Render all objects
5. Check win/lose conditions

## Browser Compatibility

- **Chrome/Edge**: Full support including sound effects
- **Firefox**: Full support including sound effects
- **Safari**: Full support including sound effects
- **Mobile browsers**: Touch controls not implemented (keyboard only)

## Future Enhancements

Potential features that could be added:
- Multiple levels with different layouts
- Power-ups (Super Mario, Fire Mario)
- Moving platforms
- More enemy types
- Background music
- Touch controls for mobile
- High score persistence
- Animated sprites instead of simple rectangles

## Technical Notes

- Uses HTML5 Canvas for rendering
- Implements basic 2D physics (gravity, collision detection)
- Sound effects generated procedurally with Web Audio API
- Responsive CSS design
- No external dependencies - pure vanilla JavaScript

Enjoy playing Super Mario 2D!
