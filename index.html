<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Mario 2D Game</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>Super Mario 2D</h1>
            <div class="game-info">
                <div class="score">Score: <span id="score">0</span></div>
                <div class="lives">Lives: <span id="lives">3</span></div>
                <div class="coins">Coins: <span id="coins">0</span></div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="400"></canvas>
        
        <div class="controls">
            <div class="instructions">
                <h3>Controls:</h3>
                <p><strong>Arrow Keys:</strong> Move left/right</p>
                <p><strong>Spacebar:</strong> Jump</p>
                <p><strong>R:</strong> Restart game</p>
            </div>
            <button id="startBtn">Start Game</button>
            <button id="pauseBtn">Pause</button>
            <button id="restartBtn">Restart</button>
        </div>
        
        <div id="gameOver" class="game-over hidden">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <button id="playAgainBtn">Play Again</button>
        </div>
        
        <div id="levelComplete" class="level-complete hidden">
            <h2>Level Complete!</h2>
            <p>Score: <span id="levelScore">0</span></p>
            <button id="nextLevelBtn">Next Level</button>
        </div>
    </div>
    
    <script src="sounds.js"></script>
    <script src="game.js"></script>
</body>
</html>
