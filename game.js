// Game constants
const CANVAS_WIDTH = 800;
const CANVAS_HEIGHT = 400;
const GRAVITY = 0.5;
const JUMP_FORCE = -12;
const MARIO_SPEED = 5;

// Game state
let gameState = {
    running: false,
    paused: false,
    score: 0,
    lives: 3,
    coins: 0,
    level: 1
};

// Get canvas and context
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Input handling
const keys = {};
document.addEventListener('keydown', (e) => {
    keys[e.code] = true;
    if (e.code === 'Space') e.preventDefault();
});
document.addEventListener('keyup', (e) => {
    keys[e.code] = false;
});

// Mario class
class Mario {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.velocityX = 0;
        this.velocityY = 0;
        this.onGround = false;
        this.direction = 1; // 1 for right, -1 for left
        this.invulnerable = false;
        this.invulnerabilityTime = 0;
    }

    update() {
        // Handle input
        if (keys['ArrowLeft']) {
            this.velocityX = -MARIO_SPEED;
            this.direction = -1;
        } else if (keys['ArrowRight']) {
            this.velocityX = MARIO_SPEED;
            this.direction = 1;
        } else {
            this.velocityX = 0;
        }

        // Jumping
        if (keys['Space'] && this.onGround) {
            this.velocityY = JUMP_FORCE;
            this.onGround = false;
            if (typeof soundManager !== 'undefined') soundManager.jump();
        }

        // Apply gravity
        this.velocityY += GRAVITY;

        // Update position
        this.x += this.velocityX;
        this.y += this.velocityY;

        // Keep Mario in bounds
        if (this.x < 0) this.x = 0;
        if (this.x + this.width > CANVAS_WIDTH) this.x = CANVAS_WIDTH - this.width;

        // Ground collision
        if (this.y + this.height >= CANVAS_HEIGHT - 50) {
            this.y = CANVAS_HEIGHT - 50 - this.height;
            this.velocityY = 0;
            this.onGround = true;
        }

        // Platform collision
        platforms.forEach(platform => {
            if (this.isColliding(platform)) {
                if (this.velocityY > 0 && this.y < platform.y) {
                    this.y = platform.y - this.height;
                    this.velocityY = 0;
                    this.onGround = true;
                }
            }
        });

        // Handle invulnerability
        if (this.invulnerable) {
            this.invulnerabilityTime--;
            if (this.invulnerabilityTime <= 0) {
                this.invulnerable = false;
            }
        }

        // Check if Mario fell off the screen
        if (this.y > CANVAS_HEIGHT) {
            this.takeDamage();
        }
    }

    isColliding(obj) {
        return this.x < obj.x + obj.width &&
               this.x + this.width > obj.x &&
               this.y < obj.y + obj.height &&
               this.y + this.height > obj.y;
    }

    takeDamage() {
        if (!this.invulnerable) {
            gameState.lives--;
            this.invulnerable = true;
            this.invulnerabilityTime = 120; // 2 seconds at 60fps
            if (typeof soundManager !== 'undefined') soundManager.damage();

            // Reset position
            this.x = 50;
            this.y = 100;
            this.velocityX = 0;
            this.velocityY = 0;

            if (gameState.lives <= 0) {
                gameOver();
            }
        }
    }

    draw() {
        ctx.save();
        
        // Flash when invulnerable
        if (this.invulnerable && Math.floor(this.invulnerabilityTime / 10) % 2) {
            ctx.globalAlpha = 0.5;
        }
        
        // Draw Mario as a simple colored rectangle for now
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // Draw Mario's hat
        ctx.fillStyle = '#8B0000';
        ctx.fillRect(this.x, this.y, this.width, 8);
        
        // Draw Mario's face
        ctx.fillStyle = '#FFDBAC';
        ctx.fillRect(this.x + 4, this.y + 8, this.width - 8, 12);
        
        // Draw Mario's overalls
        ctx.fillStyle = '#0000FF';
        ctx.fillRect(this.x + 2, this.y + 20, this.width - 4, 12);
        
        ctx.restore();
    }
}

// Platform class
class Platform {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }

    draw() {
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x, this.y, this.width, this.height);
        
        // Add some texture
        ctx.fillStyle = '#A0522D';
        for (let i = 0; i < this.width; i += 20) {
            ctx.fillRect(this.x + i, this.y, 2, this.height);
        }
    }
}

// Game objects
let mario;
let platforms = [];
let enemies = [];
let coins = [];

// Enemy class
class Enemy {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 24;
        this.velocityX = -1;
        this.velocityY = 0;
        this.alive = true;
    }

    update() {
        if (!this.alive) return;

        this.x += this.velocityX;
        this.velocityY += GRAVITY;
        this.y += this.velocityY;

        // Ground collision
        if (this.y + this.height >= CANVAS_HEIGHT - 50) {
            this.y = CANVAS_HEIGHT - 50 - this.height;
            this.velocityY = 0;
        }

        // Platform collision
        platforms.forEach(platform => {
            if (this.isColliding(platform)) {
                if (this.velocityY > 0 && this.y < platform.y) {
                    this.y = platform.y - this.height;
                    this.velocityY = 0;
                }
            }
        });

        // Reverse direction at edges
        if (this.x <= 0 || this.x + this.width >= CANVAS_WIDTH) {
            this.velocityX *= -1;
        }

        // Check collision with Mario
        if (this.alive && mario.isColliding(this)) {
            if (mario.velocityY > 0 && mario.y < this.y) {
                // Mario jumped on enemy
                this.alive = false;
                mario.velocityY = JUMP_FORCE / 2;
                gameState.score += 100;
                if (typeof soundManager !== 'undefined') soundManager.enemyDefeat();
                updateUI();
            } else {
                // Mario hit enemy
                mario.takeDamage();
            }
        }
    }

    isColliding(obj) {
        return this.x < obj.x + obj.width &&
               this.x + this.width > obj.x &&
               this.y < obj.y + obj.height &&
               this.y + this.height > obj.y;
    }

    draw() {
        if (!this.alive) return;

        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Draw eyes
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(this.x + 4, this.y + 4, 4, 4);
        ctx.fillRect(this.x + 16, this.y + 4, 4, 4);

        ctx.fillStyle = '#000000';
        ctx.fillRect(this.x + 6, this.y + 6, 2, 2);
        ctx.fillRect(this.x + 18, this.y + 6, 2, 2);
    }
}

// Coin class
class Coin {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        this.collected = false;
        this.rotation = 0;
    }

    update() {
        if (this.collected) return;

        this.rotation += 0.1;

        if (mario.isColliding(this)) {
            this.collected = true;
            gameState.coins++;
            gameState.score += 50;
            if (typeof soundManager !== 'undefined') soundManager.coin();
            updateUI();
        }
    }

    draw() {
        if (this.collected) return;

        ctx.save();
        ctx.translate(this.x + this.width/2, this.y + this.height/2);
        ctx.rotate(this.rotation);

        ctx.fillStyle = '#FFD700';
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);

        ctx.fillStyle = '#FFA500';
        ctx.fillRect(-this.width/2 + 2, -this.height/2 + 2, this.width - 4, this.height - 4);

        ctx.restore();
    }
}

// Initialize game
function initGame() {
    mario = new Mario(50, 100);

    // Create platforms
    platforms = [
        new Platform(200, 300, 150, 20),
        new Platform(400, 250, 100, 20),
        new Platform(600, 200, 120, 20),
        new Platform(300, 150, 80, 20)
    ];

    // Create enemies
    enemies = [
        new Enemy(250, 200),
        new Enemy(450, 150),
        new Enemy(650, 100)
    ];

    // Create coins
    coins = [
        new Coin(220, 270),
        new Coin(420, 220),
        new Coin(620, 170),
        new Coin(320, 120),
        new Coin(500, 300)
    ];

    gameState.score = 0;
    gameState.lives = 3;
    gameState.coins = 0;
    updateUI();
}

// Game loop
function gameLoop() {
    if (!gameState.running || gameState.paused) return;

    // Clear canvas
    ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // Draw background
    drawBackground();

    // Update game objects
    mario.update();
    enemies.forEach(enemy => enemy.update());
    coins.forEach(coin => coin.update());

    // Draw game objects
    platforms.forEach(platform => platform.draw());
    enemies.forEach(enemy => enemy.draw());
    coins.forEach(coin => coin.draw());
    mario.draw();

    // Check win condition
    if (coins.every(coin => coin.collected)) {
        levelComplete();
    }

    requestAnimationFrame(gameLoop);
}

function drawBackground() {
    // Sky gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, CANVAS_HEIGHT);
    gradient.addColorStop(0, '#87CEEB');
    gradient.addColorStop(1, '#98FB98');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // Ground
    ctx.fillStyle = '#228B22';
    ctx.fillRect(0, CANVAS_HEIGHT - 50, CANVAS_WIDTH, 50);

    // Grass texture
    ctx.fillStyle = '#32CD32';
    for (let i = 0; i < CANVAS_WIDTH; i += 10) {
        ctx.fillRect(i, CANVAS_HEIGHT - 50, 2, 10);
    }

    // Clouds
    drawCloud(100, 50);
    drawCloud(300, 80);
    drawCloud(600, 60);
}

function drawCloud(x, y) {
    ctx.fillStyle = '#FFFFFF';
    ctx.beginPath();
    ctx.arc(x, y, 20, 0, Math.PI * 2);
    ctx.arc(x + 25, y, 25, 0, Math.PI * 2);
    ctx.arc(x + 50, y, 20, 0, Math.PI * 2);
    ctx.arc(x + 25, y - 15, 15, 0, Math.PI * 2);
    ctx.fill();
}

// UI functions
function updateUI() {
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('lives').textContent = gameState.lives;
    document.getElementById('coins').textContent = gameState.coins;
}

function gameOver() {
    gameState.running = false;
    if (typeof soundManager !== 'undefined') soundManager.gameOver();
    document.getElementById('finalScore').textContent = gameState.score;
    document.getElementById('gameOver').classList.remove('hidden');
}

function levelComplete() {
    gameState.running = false;
    if (typeof soundManager !== 'undefined') soundManager.levelComplete();
    document.getElementById('levelScore').textContent = gameState.score;
    document.getElementById('levelComplete').classList.remove('hidden');
}

function startGame() {
    gameState.running = true;
    gameState.paused = false;
    document.getElementById('gameOver').classList.add('hidden');
    document.getElementById('levelComplete').classList.add('hidden');
    initGame();
    gameLoop();
}

function pauseGame() {
    gameState.paused = !gameState.paused;
    if (!gameState.paused && gameState.running) {
        gameLoop();
    }
    document.getElementById('pauseBtn').textContent = gameState.paused ? 'Resume' : 'Pause';
}

function restartGame() {
    gameState.running = false;
    gameState.paused = false;
    document.getElementById('pauseBtn').textContent = 'Pause';
    startGame();
}

// Event listeners
document.addEventListener('DOMContentLoaded', () => {
    // Button event listeners
    document.getElementById('startBtn').addEventListener('click', startGame);
    document.getElementById('pauseBtn').addEventListener('click', pauseGame);
    document.getElementById('restartBtn').addEventListener('click', restartGame);
    document.getElementById('playAgainBtn').addEventListener('click', startGame);
    document.getElementById('nextLevelBtn').addEventListener('click', () => {
        gameState.level++;
        startGame();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.code === 'KeyR') {
            restartGame();
        }
        if (e.code === 'KeyP') {
            pauseGame();
        }
    });

    // Initialize the game
    initGame();
    updateUI();

    // Draw initial state
    ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
    drawBackground();
    platforms.forEach(platform => platform.draw());
    enemies.forEach(enemy => enemy.draw());
    coins.forEach(coin => coin.draw());
    mario.draw();
});
