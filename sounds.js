// Simple sound effects using Web Audio API
class SoundManager {
    constructor() {
        this.audioContext = null;
        this.sounds = {};
        this.enabled = true;
        this.initAudio();
    }

    initAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.enabled = false;
        }
    }

    // Create simple tones for game sounds
    createTone(frequency, duration, type = 'sine') {
        if (!this.enabled || !this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        oscillator.type = type;

        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    jump() {
        this.createTone(400, 0.2, 'square');
    }

    coin() {
        this.createTone(800, 0.1, 'sine');
        setTimeout(() => this.createTone(1000, 0.1, 'sine'), 50);
    }

    enemyDefeat() {
        this.createTone(200, 0.3, 'sawtooth');
    }

    damage() {
        this.createTone(150, 0.5, 'sawtooth');
    }

    levelComplete() {
        const notes = [523, 659, 784, 1047]; // C, E, G, C
        notes.forEach((note, index) => {
            setTimeout(() => this.createTone(note, 0.3, 'sine'), index * 150);
        });
    }

    gameOver() {
        const notes = [392, 370, 349, 330]; // Descending notes
        notes.forEach((note, index) => {
            setTimeout(() => this.createTone(note, 0.4, 'triangle'), index * 200);
        });
    }
}

// Create global sound manager
const soundManager = new SoundManager();
