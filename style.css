* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    padding: 15px;
    text-align: center;
    max-height: 95vh;
    max-width: 95vw;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.game-header {
    margin-bottom: 10px;
    flex-shrink: 0;
}

.game-header h1 {
    color: #FF6B6B;
    font-size: 1.8em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 8px;
}

.game-info {
    display: flex;
    justify-content: space-around;
    background: #f0f0f0;
    padding: 8px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 1em;
}

.score { color: #FF6B6B; }
.lives { color: #4ECDC4; }
.coins { color: #FFD93D; }

#gameCanvas {
    border: 3px solid #333;
    border-radius: 10px;
    background: #87CEEB;
    display: block;
    margin: 0 auto;
    flex-shrink: 0;
    max-width: 100%;
    max-height: 60vh;
}

.controls {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.instructions {
    background: #f9f9f9;
    padding: 10px;
    border-radius: 8px;
    border-left: 4px solid #FF6B6B;
    font-size: 0.9em;
}

.instructions h3 {
    color: #333;
    margin-bottom: 5px;
    font-size: 1em;
}

.instructions p {
    margin: 2px 0;
    color: #666;
}

button {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    margin: 0 3px;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

button:active {
    transform: translateY(0);
}

.game-over, .level-complete {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    text-align: center;
    z-index: 1000;
}

.game-over h2, .level-complete h2 {
    color: #FF6B6B;
    margin-bottom: 15px;
    font-size: 2em;
}

.hidden {
    display: none;
}

@media (max-width: 900px) {
    .game-container {
        padding: 8px;
        max-height: 98vh;
    }

    #gameCanvas {
        width: 100%;
        max-width: 800px;
        height: auto;
        max-height: 50vh;
    }

    .game-header h1 {
        font-size: 1.5em;
        margin-bottom: 5px;
    }

    .game-info {
        flex-direction: row;
        gap: 5px;
        font-size: 0.9em;
        padding: 6px;
    }

    .instructions {
        padding: 8px;
        font-size: 0.8em;
    }

    .instructions h3 {
        font-size: 0.9em;
        margin-bottom: 3px;
    }

    button {
        padding: 6px 12px;
        font-size: 0.8em;
    }
}

@media (max-height: 600px) {
    .game-header h1 {
        font-size: 1.3em;
        margin-bottom: 3px;
    }

    .game-info {
        padding: 4px;
        font-size: 0.8em;
    }

    #gameCanvas {
        max-height: 45vh;
    }

    .instructions {
        display: none;
    }

    .controls {
        margin-top: 5px;
        gap: 5px;
    }
}
