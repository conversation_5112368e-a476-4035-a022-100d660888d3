* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    padding: 20px;
    text-align: center;
}

.game-header {
    margin-bottom: 20px;
}

.game-header h1 {
    color: #FF6B6B;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 10px;
}

.game-info {
    display: flex;
    justify-content: space-around;
    background: #f0f0f0;
    padding: 10px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.1em;
}

.score { color: #FF6B6B; }
.lives { color: #4ECDC4; }
.coins { color: #FFD93D; }

#gameCanvas {
    border: 3px solid #333;
    border-radius: 10px;
    background: #87CEEB;
    display: block;
    margin: 0 auto;
}

.controls {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.instructions {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #FF6B6B;
}

.instructions h3 {
    color: #333;
    margin-bottom: 10px;
}

.instructions p {
    margin: 5px 0;
    color: #666;
}

button {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    margin: 0 5px;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

button:active {
    transform: translateY(0);
}

.game-over, .level-complete {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    text-align: center;
    z-index: 1000;
}

.game-over h2, .level-complete h2 {
    color: #FF6B6B;
    margin-bottom: 15px;
    font-size: 2em;
}

.hidden {
    display: none;
}

@media (max-width: 900px) {
    .game-container {
        padding: 10px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 800px;
        height: auto;
    }
    
    .game-header h1 {
        font-size: 2em;
    }
    
    .game-info {
        flex-direction: column;
        gap: 5px;
    }
}
